
import { useState } from 'react';
import { WalletContextProvider } from './contexts/WalletContextProvider';
import EnhancedTokenCreator from './components/EnhancedTokenCreator';
import DemoTokenCreator from './components/DemoTokenCreator';
import './App.css';

function App() {
  const [showDemo, setShowDemo] = useState(true);

  return (
    <WalletContextProvider>
      <div className="min-h-screen bg-gray-100 py-8">
        {/* Toggle Button */}
        <div className="max-w-2xl mx-auto mb-6 text-center">
          <button
            onClick={() => setShowDemo(!showDemo)}
            className="bg-purple-600 text-white px-6 py-2 rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500"
          >
            {showDemo ? 'Switch to Full Version (Wallet Required)' : 'Switch to Demo Version'}
          </button>
        </div>
        
        {showDemo ? <DemoTokenCreator /> : <EnhancedTokenCreator />}
      </div>
    </WalletContextProvider>
  );
}

export default App;
