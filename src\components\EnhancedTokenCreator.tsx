import React, { useState, useRef } from 'react';
import { useWallet } from '@solana/wallet-adapter-react';
import { WalletMultiButton } from '@solana/wallet-adapter-react-ui';
import { metadataService, type TokenMetadata, type TokenCreationResult } from '../services/metadataService';

interface TokenForm {
  name: string;
  symbol: string;
  description: string;
  decimals: number;
  supply: number;
  revokeAuthorities: boolean;
  externalUrl: string;
  image?: File;
}

export const EnhancedTokenCreator: React.FC = () => {
  const { connected, wallet, publicKey } = useWallet();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [form, setForm] = useState<TokenForm>({
    name: '',
    symbol: '',
    description: '',
    decimals: 9,
    supply: 1000000,
    revokeAuthorities: true,
    externalUrl: '',
  });
  const [isCreating, setIsCreating] = useState(false);
  const [result, setResult] = useState<TokenCreationResult | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!connected || !wallet?.adapter) {
      setError('Please connect your wallet first');
      return;
    }

    // Validate form
    if (!form.name.trim() || !form.symbol.trim()) {
      setError('Name and symbol are required');
      return;
    }

    if (form.symbol.length > 10) {
      setError('Symbol must be 10 characters or less');
      return;
    }

    setIsCreating(true);
    setError(null);
    setResult(null);

    try {
      const tokenData: TokenMetadata = {
        name: form.name.trim(),
        symbol: form.symbol.trim().toUpperCase(),
        description: form.description.trim() || `${form.name} token created on Solana`,
        decimals: form.decimals,
        supply: form.supply,
        revokeAuthorities: form.revokeAuthorities,
        externalUrl: form.externalUrl.trim(),
        image: form.image,
      };

      const tokenResult = await metadataService.createTokenWithMetadata(
        wallet.adapter,
        tokenData
      );
      
      setResult(tokenResult);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create token');
    } finally {
      setIsCreating(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    const checked = (e.target as HTMLInputElement).checked;
    
    setForm(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : type === 'number' ? Number(value) : value,
    }));
  };

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Validate image
    const validation = metadataService.validateImage(file);
    if (!validation.valid) {
      setError(validation.error || 'Invalid image file');
      return;
    }

    setForm(prev => ({ ...prev, image: file }));
    
    // Create preview
    const reader = new FileReader();
    reader.onload = (e) => {
      setImagePreview(e.target?.result as string);
    };
    reader.readAsDataURL(file);
    setError(null);
  };

  const removeImage = () => {
    setForm(prev => ({ ...prev, image: undefined }));
    setImagePreview(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <div className="max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      <h1 className="text-3xl font-bold text-center mb-8 text-gray-800">
        Solana Token Creator
      </h1>
      
      <div className="mb-6 text-center">
        <WalletMultiButton />
        {connected && publicKey && (
          <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-md">
            <p className="text-sm text-green-700">
              <strong>Connected:</strong> {publicKey.toString().slice(0, 8)}...{publicKey.toString().slice(-8)}
            </p>
            <p className="text-xs text-green-600 mt-1">
              Network: Solana Devnet
            </p>
          </div>
        )}
      </div>

      {!connected ? (
        <div className="text-center text-gray-600">
          <p>Please connect your Phantom wallet to create a token.</p>
          <div className="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-blue-800 mb-2">Getting Started</h3>
            <ol className="text-blue-700 space-y-1 text-sm text-left">
              <li>1. Install the Phantom wallet browser extension</li>
              <li>2. Create or import a wallet</li>
              <li>3. Switch to Devnet in Phantom settings</li>
              <li>4. Get some Devnet SOL from a faucet</li>
              <li>5. Connect your wallet above to start creating tokens</li>
            </ol>
          </div>
        </div>
      ) : (
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Image Upload Section */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Token Image (Optional)
            </label>
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-4">
              {imagePreview ? (
                <div className="text-center">
                  <img 
                    src={imagePreview} 
                    alt="Token preview" 
                    className="mx-auto h-32 w-32 object-cover rounded-lg mb-2"
                  />
                  <button
                    type="button"
                    onClick={removeImage}
                    className="text-red-600 hover:text-red-800 text-sm"
                  >
                    Remove Image
                  </button>
                </div>
              ) : (
                <div className="text-center">
                  <svg className="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                    <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                  </svg>
                  <div className="mt-2">
                    <button
                      type="button"
                      onClick={() => fileInputRef.current?.click()}
                      className="text-blue-600 hover:text-blue-800"
                    >
                      Upload an image
                    </button>
                    <p className="text-xs text-gray-500 mt-1">PNG, JPG up to 5MB</p>
                  </div>
                </div>
              )}
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                onChange={handleImageChange}
                className="hidden"
              />
            </div>
          </div>

          {/* Basic Token Info */}
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
              Token Name *
            </label>
            <input
              type="text"
              id="name"
              name="name"
              value={form.name}
              onChange={handleInputChange}
              required
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="e.g., My Awesome Token"
            />
          </div>

          <div>
            <label htmlFor="symbol" className="block text-sm font-medium text-gray-700 mb-2">
              Token Symbol *
            </label>
            <input
              type="text"
              id="symbol"
              name="symbol"
              value={form.symbol}
              onChange={handleInputChange}
              required
              maxLength={10}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="e.g., MAT"
            />
          </div>

          <div>
            <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
              Description *
            </label>
            <textarea
              id="description"
              name="description"
              value={form.description}
              onChange={handleInputChange}
              required
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Describe your token..."
            />
          </div>

          <div>
            <label htmlFor="externalUrl" className="block text-sm font-medium text-gray-700 mb-2">
              Website URL (Optional)
            </label>
            <input
              type="url"
              id="externalUrl"
              name="externalUrl"
              value={form.externalUrl}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="https://yourproject.com"
            />
          </div>

          {/* Token Configuration */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label htmlFor="decimals" className="block text-sm font-medium text-gray-700 mb-2">
                Decimals
              </label>
              <input
                type="number"
                id="decimals"
                name="decimals"
                value={form.decimals}
                onChange={handleInputChange}
                min="0"
                max="9"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div>
              <label htmlFor="supply" className="block text-sm font-medium text-gray-700 mb-2">
                Total Supply
              </label>
              <input
                type="number"
                id="supply"
                name="supply"
                value={form.supply}
                onChange={handleInputChange}
                min="1"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>

          <div className="flex items-center">
            <input
              type="checkbox"
              id="revokeAuthorities"
              name="revokeAuthorities"
              checked={form.revokeAuthorities}
              onChange={handleInputChange}
              className="mr-2"
            />
            <label htmlFor="revokeAuthorities" className="text-sm text-gray-700">
              Revoke mint and freeze authorities (recommended for security)
            </label>
          </div>

          {/* Info Box */}
          <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
            <p className="text-sm text-blue-800">
              <strong>Enhanced Features:</strong> This version includes full metadata support with image upload, 
              description, and external URL. Your token will be fully compatible with wallets and marketplaces.
            </p>
          </div>

          <button
            type="submit"
            disabled={isCreating}
            className="w-full bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isCreating ? 'Creating Token...' : 'Create Token with Metadata'}
          </button>
        </form>
      )}

      {error && (
        <div className="mt-6 p-4 bg-red-100 border border-red-400 text-red-700 rounded-md">
          <h3 className="font-bold">Error:</h3>
          <p>{error}</p>
        </div>
      )}

      {result && (
        <div className="mt-6 p-4 bg-green-100 border border-green-400 text-green-700 rounded-md">
          <h3 className="font-bold mb-2">Token Created Successfully!</h3>
          <div className="space-y-2 text-sm">
            <p><strong>Mint Address:</strong> 
              <span className="font-mono text-xs bg-gray-100 px-1 rounded">{result.mint}</span>
            </p>
            <p><strong>Metadata URI:</strong> 
              <a 
                href={result.metadataUri} 
                target="_blank" 
                rel="noopener noreferrer"
                className="text-blue-600 hover:underline ml-1"
              >
                View Metadata
              </a>
            </p>
            {result.imageUrl && (
              <p><strong>Image URL:</strong> 
                <a 
                  href={result.imageUrl} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:underline ml-1"
                >
                  View Image
                </a>
              </p>
            )}
            <p><strong>Transaction:</strong> 
              <a 
                href={`https://explorer.solana.com/tx/${result.signature}?cluster=devnet`}
                target="_blank" 
                rel="noopener noreferrer"
                className="text-blue-600 hover:underline ml-1"
              >
                View on Explorer
              </a>
            </p>
            <p><strong>Token:</strong> 
              <a 
                href={`https://explorer.solana.com/address/${result.mint}?cluster=devnet`}
                target="_blank" 
                rel="noopener noreferrer"
                className="text-blue-600 hover:underline ml-1"
              >
                View Token on Explorer
              </a>
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default EnhancedTokenCreator;