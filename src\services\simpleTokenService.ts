import {
  createMint,
  getOrCreateAssociatedTokenAccount,
  mintTo,
  setAuthority,
  AuthorityType,
} from '@solana/spl-token';
import { Connection, PublicKey } from '@solana/web3.js';
import type { WalletContextState } from '@solana/wallet-adapter-react';

export interface TokenMetadata {
  name: string;
  symbol: string;
  description: string;
  decimals: number;
  supply: number;
}

export interface CreateTokenResult {
  signature: string;
  mintAddress: string;
  explorerUrl: string;
  tokenExplorerUrl: string;
}

export class SimpleTokenService {
  private connection: Connection;

  constructor(endpoint: string = 'https://api.devnet.solana.com') {
    this.connection = new Connection(endpoint, 'confirmed');
  }

  async createToken(
    wallet: WalletContextState,
    metadata: TokenMetadata,
    revokeAuthorities: boolean = true
  ): Promise<CreateTokenResult> {
    if (!wallet.publicKey || !wallet.signTransaction) {
      throw new Error('Wallet not connected');
    }

    console.log('Creating token with metadata:', metadata);

    console.log('Creating mint...');
    const mint = await createMint(
      this.connection,
      {
        publicKey: wallet.publicKey,
        signTransaction: wallet.signTransaction,
        signAllTransactions: wallet.signAllTransactions,
      } as any,
      wallet.publicKey, // mint authority
      wallet.publicKey, // freeze authority (can be null)
      metadata.decimals
    );

    console.log('Mint created:', mint.toString());

    // Get or create associated token account
    const tokenAccount = await getOrCreateAssociatedTokenAccount(
      this.connection,
      {
        publicKey: wallet.publicKey,
        signTransaction: wallet.signTransaction,
        signAllTransactions: wallet.signAllTransactions,
      } as any,
      mint,
      wallet.publicKey
    );

    console.log('Token account:', tokenAccount.address.toString());

    // Mint tokens to the account
    const mintAmount = BigInt(metadata.supply * Math.pow(10, metadata.decimals));
    const mintSignature = await mintTo(
      this.connection,
      {
        publicKey: wallet.publicKey,
        signTransaction: wallet.signTransaction,
        signAllTransactions: wallet.signAllTransactions,
      } as any,
      mint,
      tokenAccount.address,
      wallet.publicKey,
      mintAmount
    );

    console.log('Tokens minted:', mintSignature);

    // Revoke authorities if requested
    if (revokeAuthorities) {
      await this.revokeAuthorities(wallet, mint);
    }

    return {
      signature: mintSignature,
      mintAddress: mint.toString(),
      explorerUrl: `https://explorer.solana.com/tx/${mintSignature}?cluster=devnet`,
      tokenExplorerUrl: `https://explorer.solana.com/address/${mint.toString()}?cluster=devnet`,
    };
  }

  private async revokeAuthorities(wallet: WalletContextState, mint: PublicKey): Promise<void> {
    if (!wallet.publicKey || !wallet.signTransaction) {
      throw new Error('Wallet not connected');
    }

    console.log('Revoking mint authority...');
    
    try {
      // Revoke mint authority
      await setAuthority(
        this.connection,
        {
          publicKey: wallet.publicKey,
          signTransaction: wallet.signTransaction,
          signAllTransactions: wallet.signAllTransactions,
        } as any,
        mint,
        wallet.publicKey,
        AuthorityType.MintTokens,
        null // Setting to null revokes the authority
      );

      console.log('Mint authority revoked');

      // Revoke freeze authority
      await setAuthority(
        this.connection,
        {
          publicKey: wallet.publicKey,
          signTransaction: wallet.signTransaction,
          signAllTransactions: wallet.signAllTransactions,
        } as any,
        mint,
        wallet.publicKey,
        AuthorityType.FreezeAccount,
        null // Setting to null revokes the authority
      );

      console.log('Freeze authority revoked');
    } catch (error) {
      console.error('Error revoking authorities:', error);
      throw error;
    }
  }
}