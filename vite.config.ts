import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import tailwindcss from '@tailwindcss/vite'
import { resolve } from 'path'

// https://vite.dev/config/
export default defineConfig({
  plugins: [react(), tailwindcss()],
  server: {
    host: '0.0.0.0',
    port: 12000,
    cors: true,
    headers: {
      'X-Frame-Options': 'ALLOWALL',
    },
    allowedHosts: ['work-1-bdrzupmseqhdcakf.prod-runtime.all-hands.dev'],
  },
  define: {
    global: 'globalThis',
  },
  resolve: {
    alias: {
      buffer: resolve(__dirname, 'node_modules/buffer'),
      'process/': resolve(__dirname, 'node_modules/process/'),
      process: resolve(__dirname, 'node_modules/process/browser'),
      stream: resolve(__dirname, 'node_modules/stream-browserify'),
      crypto: resolve(__dirname, 'node_modules/crypto-browserify'),
      'stream/promises': resolve(__dirname, 'node_modules/stream-browserify'),
    },
  },
  optimizeDeps: {
    include: ['buffer', 'process'],
  },
})
