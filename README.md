# Solana Token Creator with Metadata

A modern React application for creating Solana tokens with full metadata support using Phantom wallet integration and Metaplex standards.

## Features

### 🚀 Enhanced Token Creation
- **Full Metadata Support**: Create tokens with images, descriptions, and external URLs
- **Image Upload**: Upload token images with automatic hosting via imgbb.com
- **Metadata Hosting**: Automatic JSON metadata hosting using jsonbin.io
- **Metaplex Standards**: Full compliance with Metaplex token metadata standards

### 💼 Wallet Integration
- **Phantom Wallet**: Seamless connection with Phantom wallet
- **Secure Transactions**: All token creation happens on-chain with proper signatures
- **Authority Management**: Option to revoke mint and freeze authorities for security

### 🎨 Modern UI/UX
- **Tailwind CSS v4**: Beautiful, responsive design with modern styling
- **Interactive Forms**: Real-time validation and user feedback
- **Image Preview**: Live preview of uploaded token images
- **Demo Mode**: Try the interface without wallet connection

## Technology Stack

- **Frontend**: React 18 + TypeScript + Vite
- **Styling**: Tailwind CSS v4
- **Blockchain**: Solana Web3.js + Metaplex
- **Wallet**: Solana Wallet Adapter (Phantom)
- **Image Hosting**: imgbb.com (free tier)
- **Metadata Hosting**: jsonbin.io (free tier)

## Getting Started

### Prerequisites
- Node.js 18+ and npm
- Phantom wallet browser extension
- Some SOL for transaction fees

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd solana-token-creator
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

4. Open your browser and navigate to `http://localhost:12000`

### Configuration

The application uses free hosting services for images and metadata:

- **Images**: Uploaded to imgbb.com (5MB limit, free tier)
- **Metadata**: Stored on jsonbin.io (free tier)

For production use, consider upgrading to paid tiers or using your own hosting solutions.

## Usage

### Demo Mode
1. Visit the application
2. Fill out the token creation form
3. Upload an image (optional)
4. Click "Create Token with Metadata (Demo)" to see the interface

### Full Functionality
1. Click "Switch to Full Version (Wallet Required)"
2. Connect your Phantom wallet
3. Fill out all token details:
   - Token name and symbol
   - Description
   - Image upload
   - Website URL (optional)
   - Decimals and supply
   - Authority settings
4. Click "Create Token with Metadata"
5. Approve the transaction in your wallet

## Token Metadata Structure

The application creates tokens following the Metaplex Token Metadata standard:

```json
{
  "name": "Token Name",
  "symbol": "SYMBOL",
  "description": "Token description",
  "image": "https://hosted-image-url",
  "external_url": "https://project-website.com",
  "attributes": [],
  "properties": {
    "files": [
      {
        "uri": "https://hosted-image-url",
        "type": "image/png"
      }
    ],
    "category": "image"
  }
}
```

## Development

### Project Structure
```
src/
├── components/
│   ├── DemoTokenCreator.tsx      # Demo interface
│   ├── EnhancedTokenCreator.tsx  # Full functionality
│   └── WalletConnectionModal.tsx # Wallet connection UI
├── contexts/
│   └── WalletContextProvider.tsx # Wallet state management
├── services/
│   └── metadataService.ts        # Token creation logic
└── App.tsx                       # Main application
```

### Key Dependencies
- `@solana/web3.js` - Solana blockchain interaction
- `@solana/wallet-adapter-react` - Wallet integration
- `@metaplex-foundation/mpl-token-metadata` - Token metadata standards
- `@metaplex-foundation/umi` - Metaplex unified interface
- `@tailwindcss/vite` - Tailwind CSS v4 integration

### Building for Production
```bash
npm run build
```

The built files will be in the `dist/` directory, ready for deployment to any static hosting service.

## Deployment

This application can be deployed to any static hosting service:

- **Vercel**: Connect your GitHub repository for automatic deployments
- **Netlify**: Drag and drop the `dist/` folder or connect via Git
- **GitHub Pages**: Use GitHub Actions for automated deployment
- **AWS S3**: Upload the `dist/` folder to an S3 bucket with static hosting

## Security Considerations

- **Private Keys**: Never expose private keys in the frontend code
- **Authority Revocation**: Consider revoking mint/freeze authorities for security
- **Transaction Validation**: Always validate transactions before signing
- **HTTPS**: Use HTTPS in production for secure wallet connections

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is open source and available under the MIT License.
