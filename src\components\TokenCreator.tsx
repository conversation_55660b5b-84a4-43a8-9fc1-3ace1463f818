import React, { useState } from 'react';
import { useWallet } from '@solana/wallet-adapter-react';
import { WalletMultiButton } from '@solana/wallet-adapter-react-ui';
import { SimpleTokenService } from '../services/simpleTokenService';
import type { TokenMetadata, CreateTokenResult } from '../services/simpleTokenService';

const TokenCreator: React.FC = () => {
  const wallet = useWallet();
  
  const [formData, setFormData] = useState<TokenMetadata>({
    name: '',
    symbol: '',
    description: '',
    decimals: 9,
    supply: 1000000,
  });
  

  const [isCreating, setIsCreating] = useState(false);
  const [result, setResult] = useState<CreateTokenResult | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [revokeAuthorities, setRevokeAuthorities] = useState(true);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'decimals' || name === 'supply' ? Number(value) : value,
    }));
  };



  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!wallet.connected || !wallet.publicKey) {
      setError('Please connect your wallet first');
      return;
    }

    setIsCreating(true);
    setError(null);
    setResult(null);

    try {
      const tokenService = new SimpleTokenService();
      
      // Create token
      console.log('Creating token...');
      const tokenResult = await tokenService.createToken(
        wallet,
        formData,
        revokeAuthorities
      );
      
      setResult(tokenResult);
      console.log('Token created successfully:', tokenResult);
      
    } catch (err) {
      console.error('Error creating token:', err);
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
    } finally {
      setIsCreating(false);
    }
  };

  return (
    <div className="max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      <h1 className="text-3xl font-bold text-center mb-8 text-gray-800">
        Solana Token Creator
      </h1>
      
      <div className="mb-6 text-center">
        <WalletMultiButton />
        {wallet.connected && wallet.publicKey && (
          <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-md">
            <p className="text-sm text-green-700">
              <strong>Connected:</strong> {wallet.publicKey.toString().slice(0, 8)}...{wallet.publicKey.toString().slice(-8)}
            </p>
            <p className="text-xs text-green-600 mt-1">
              Network: Solana Devnet
            </p>
          </div>
        )}
      </div>

      {!wallet.connected ? (
        <div className="text-center text-gray-600">
          <p>Please connect your Phantom wallet to create a token.</p>
          <div className="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-blue-800 mb-2">Getting Started</h3>
            <ol className="text-blue-700 space-y-1 text-sm text-left">
              <li>1. Install the Phantom wallet browser extension</li>
              <li>2. Create or import a wallet</li>
              <li>3. Switch to Devnet in Phantom settings</li>
              <li>4. Get some Devnet SOL from a faucet</li>
              <li>5. Connect your wallet above to start creating tokens</li>
            </ol>
          </div>
        </div>
      ) : (
        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
              Token Name *
            </label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              required
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="e.g., My Awesome Token"
            />
          </div>

          <div>
            <label htmlFor="symbol" className="block text-sm font-medium text-gray-700 mb-2">
              Token Symbol *
            </label>
            <input
              type="text"
              id="symbol"
              name="symbol"
              value={formData.symbol}
              onChange={handleInputChange}
              required
              maxLength={10}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="e.g., MAT"
            />
          </div>

          <div>
            <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
              Description *
            </label>
            <textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              required
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Describe your token..."
            />
          </div>

          <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
            <p className="text-sm text-yellow-800">
              <strong>Note:</strong> This simplified version creates a basic SPL token without metadata. 
              Image upload and metadata features will be added in a future update.
            </p>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label htmlFor="decimals" className="block text-sm font-medium text-gray-700 mb-2">
                Decimals
              </label>
              <input
                type="number"
                id="decimals"
                name="decimals"
                value={formData.decimals}
                onChange={handleInputChange}
                min="0"
                max="9"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div>
              <label htmlFor="supply" className="block text-sm font-medium text-gray-700 mb-2">
                Total Supply
              </label>
              <input
                type="number"
                id="supply"
                name="supply"
                value={formData.supply}
                onChange={handleInputChange}
                min="1"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>

          <div className="flex items-center">
            <input
              type="checkbox"
              id="revokeAuthorities"
              checked={revokeAuthorities}
              onChange={(e) => setRevokeAuthorities(e.target.checked)}
              className="mr-2"
            />
            <label htmlFor="revokeAuthorities" className="text-sm text-gray-700">
              Revoke mint and freeze authorities (recommended for security)
            </label>
          </div>

          <button
            type="submit"
            disabled={isCreating}
            className="w-full bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isCreating ? 'Creating Token...' : 'Create Token'}
          </button>
        </form>
      )}

      {error && (
        <div className="mt-6 p-4 bg-red-100 border border-red-400 text-red-700 rounded-md">
          <h3 className="font-bold">Error:</h3>
          <p>{error}</p>
        </div>
      )}

      {result && (
        <div className="mt-6 p-4 bg-green-100 border border-green-400 text-green-700 rounded-md">
          <h3 className="font-bold mb-2">Token Created Successfully!</h3>
          <div className="space-y-2 text-sm">
            <p><strong>Mint Address:</strong> {result.mintAddress}</p>
            <p><strong>Transaction:</strong> 
              <a 
                href={result.explorerUrl} 
                target="_blank" 
                rel="noopener noreferrer"
                className="text-blue-600 hover:underline ml-1"
              >
                View on Explorer
              </a>
            </p>
            <p><strong>Token:</strong> 
              <a 
                href={result.tokenExplorerUrl} 
                target="_blank" 
                rel="noopener noreferrer"
                className="text-blue-600 hover:underline ml-1"
              >
                View Token on Explorer
              </a>
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default TokenCreator;